import os

# LLM提供商选择（gemini或ollama）
LLM_PROVIDER = os.environ.get('LLM_PROVIDER', 'gemini')

# Ollama配置
OLLAMA_BASE_URL = os.environ.get('OLLAMA_BASE_URL', 'http://************:11434/')
OLLAMA_MODEL = os.environ.get('OLLAMA_MODEL', 'llama3.2:3b')

# Google Gemini配置
GOOGLE_API_KEY = os.environ.get('GOOGLE_API_KEY', 'AIzaSyA7-ue1fLWSi1W59QFiSqeU5GXQdFp964c')
GOOGLE_MODEL = os.environ.get('GOOGLE_MODEL', 'gemini-2.0-flash')

# ChromaDB配置
CHROMA_PERSIST_DIR = os.environ.get('CHROMA_PERSIST_DIR', '/home/<USER>/chroma_space')
CHROMA_HOST = os.environ.get('CHROMA_HOST', '************')
CHROMA_PORT = os.environ.get('CHROMA_PORT', '8000')

# 文本分割器配置
SPLITTER_CHUNK_SIZE = int(os.environ.get('SPLITTER_CHUNK_SIZE', '1000'))
SPLITTER_CHUNK_OVERLAP = int(os.environ.get('SPLITTER_CHUNK_OVERLAP', '100'))

# 调试模式配置
DEBUG_MODE = os.environ.get('DEBUG_MODE', 'True').lower() in ('true', '1', 't')