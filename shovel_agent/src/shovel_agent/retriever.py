from langchain_chroma import Chroma
from langchain_ollama import OllamaEmbeddings
from chromadb import HttpClient
import os
from dotenv import load_dotenv
load_dotenv()
embeddings = OllamaEmbeddings(base_url=os.getenv('API_BASE'), model=os.getenv('MODEL'))
chroma_store = Chroma(persist_directory=os.getenv('CHROMA_PERSIST_DIR'), embedding_function=embeddings, client=HttpClient(host=os.getenv('CHROMA_HOST')))

from langchain.tools.retriever import create_retriever_tool

retriever_tool = create_retriever_tool(
    chroma_store.as_retriever(search_kwargs={'k': 3}),
    "defi_specialized_knowledge_library",
    "Augmented defi Knowledge library on loan,liquidity, stake,re-stake and yield farming",
)