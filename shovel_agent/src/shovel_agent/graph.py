"""Graphs that extract memories on a schedule."""

import asyncio
import logging

from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.store.base import BaseStore
from langchain_ollama import ChatOllama

from shovel_agent import configuration, tools, utils, retriever
from shovel_agent.state import State
from langgraph.prebuilt import ToolNode

logger = logging.getLogger(__name__)

import os
from dotenv import load_dotenv
load_dotenv()
llm = ChatOllama(model=os.getenv('MODEL'), base_url=os.getenv('API_BASE'))


async def call_model(state: State, config: RunnableConfig, *, store: BaseStore) -> dict:
    """Extract the user's state from the conversation and update the memory."""
    configurable = configuration.Configuration.from_runnable_config(config)

#     # Retrieve the most recent memories for context
#     memories = await store.asearch(
#         ("memories", configurable.user_id),
#         query=str([m.content for m in state.messages[-3:]]),
#         limit=10,
#     )
#
#     # Format memories for inclusion in the prompt
#     formatted = "\n".join(f"[{mem.key}]: {mem.value} (similarity: {mem.score})" for mem in memories)
#     if formatted:
#         formatted = f"""
# <memories>
# {formatted}
# </memories>"""

    # Prepare the system prompt with user memories and current time
    # This helps the model understand the context and temporal relevance
    # sys = configurable.system_prompt.format(
    #     user_info=formatted
    # )
    # TODO 暂时不使用long term memory
    sys = configurable.system_prompt.format(
        user_info=''
    )

    # Invoke the language model with the prepared prompt and tools
    # "bind_tools" gives the LLM the JSON schema for all tools in the list so it knows how
    # to use them.
    msg = await llm.bind_tools([retriever.retriever_tool]).ainvoke(
        [{"role": "system", "content": sys}, *state.messages],
        {"configurable": utils.split_model_and_provider(configurable.model)},
    )
    # print(sys)
    return {"messages": [msg]}


async def store_memory(state: State, config: RunnableConfig, *, store: BaseStore):
    # Extract tool calls from the last message
    tool_calls = state.messages[-1].tool_calls

    # Concurrently execute all upsert_memory calls
    saved_memories = await asyncio.gather(
        *(
            tools.upsert_memory(**tc["args"], config=config, store=store)
            for tc in tool_calls
        )
    )

    # Format the results of memory storage operations
    # This provides confirmation to the model that the actions it took were completed
    results = [
        {
            "role": "tool",
            "content": mem,
            "tool_call_id": tc["id"],
        }
        for tc, mem in zip(tool_calls, saved_memories)
    ]
    return {"messages": results}

toolExecNode = ToolNode([retriever.retriever_tool])

def route_message(state: State):
    """Determine the next step based on the presence of tool calls."""
    msg = state.messages[-1]
    if msg.tool_calls:
        if msg.tool_calls[-1]["name"] == "upsert_memory":
            # If there are tool calls, we need to store memories
            return "store_memory"
        else:
            return toolExecNode.name
    # Otherwise, finish; user can send the next message
    return END



# Create the graph + all nodes
builder = StateGraph(State, config_schema=configuration.Configuration)

# Define the flow of the memory extraction process
builder.add_node(call_model)
builder.add_edge("__start__", "call_model")
builder.add_conditional_edges("call_model", route_message, [toolExecNode.name, END])
# TODO 暂不使用 store_memory
# builder.add_node(store_memory)
# builder.add_conditional_edges("call_model", route_message, ["store_memory", toolExecNode.name, END])
# builder.add_edge("store_memory", "call_model")
builder.add_node(toolExecNode)
builder.add_edge(toolExecNode.name, "call_model")
graph = builder.compile()
graph.name = "AlphaFiAgent"


__all__ = ["graph", "builder"]
